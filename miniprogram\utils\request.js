let isRefreshing = true; // 请求锁
let pendings = []; // 请求列表
const pathArr = [
    "pages/buy-goods/index",
    "pages/pageHome/pageHome"
];  // 不需要登录的路径
const myUrl = "http://www.baidu.com";
const baseUrl = getApp().globalData.apiUrl || myUrl;  // 基础路径

function request({method, url, data, options = {
    needLogin: true
}}) {
    const token = wx.getStorageSync('tokenInfo')
    const pages = getCurrentPages();
    const router = pages[pages.length - 1]['route']; // 当前路由
    if (pathArr.includes(router)) options.needLogin = false; // 当前路径是否需要登录
    return new Promise((resolve, reject) => {
        // 需要登录 但是 token不存在 跳转登录
        if (!token && options.needLogin) {
            wx.redirectTo({
                url: '/pages/login/login',
            })
            return
        }
        wx.showNavigationBarLoading();
        wx.showLoading({
            title: '数据加载中...',
            mask: true
        })
        // 请求主体
        wx.request({
            url: baseUrl + url,
            header:{
                Authorization:token.access_token
            },
            method,
            data,
            success(res) {
                let code = res.data.code
                if (code == '9898') {
                    resolve(res.data)
                } else if (code == '200') {
                    if (isRefreshing) {
                        updateToken();
                        isRefreshing = false;
                        pendings.push(() => {
                            resolve(request({method, url, data, options}))
                        })
                    }
                } else {
                    resolve(res.data);
                }
            },
            fail(err) {
                reject(err);
            },
            complete() {
                wx.hideNavigationBarLoading();
                wx.hideLoading();
            }
        })
    })
}
// 刷新token
function updateToken() {
    const token = wx.getStorageSync('tokenInfo');
    const userId = token.user_id;
    const refreshToken = token.refresh_token;
    wx.request({
        url: baseUrl + '/user/refreshToken',
        method: 'POST',
        data: {
            userId: userId,
            refreshToken: refreshToken
        },
        success(res) {
            let code = res.data.code
            if (code == '200') {
                wx.setStorageSync('tokenInfo', {
                    access_token: res.data.data.accessToken,
                    refresh_token: res.data.data.refreshToken,
                    user_id: res.data.data.userId,
                });
                pendings.map((callback) => {
                    callback();
                })
                isRefreshing = true;
            } else {
                toLogin();
            }
        }
    })
}
// 前往登录页面 清空状态
function toLogin() {
    wx.showToast({
        title: '登录失效,请重新登录',
        icon: "none",
        success: () => {
            setTimeout(() => {
                wx.redirectTo({
                    url: '/pages/login/login',
                })
                pendings = [];
                isRefreshing = true;
            }, 1200);
        }
    })
}
module.exports = {
    request
};
// 同时支持 ES6 导入
exports.request = request;