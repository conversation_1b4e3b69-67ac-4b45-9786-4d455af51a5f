import { getOpenidAPI } from "../../utils/http";
// pages/pageHome/pageHome.ts
Page({
  /**
   * 页面的初始数据
   */
  data: {
    list: [
      {
        name: "节电器",
        logo: "../../images/logo/HLK节电器.jpg",
        id: "/pages/pageConnect/pageConnect",
      },
      {
        name: "远程开关机",
        logo: "../../images/logo/海凌科远程开关机.jpg",
        id: "wx00706a831f79795c",
      },
      {
        name: "跌倒守护",
        logo: "../../images/logo/HLK跌倒守护.jpg",
        id: "wxc2043981478a9017",
      },
      {
        name: "雷达感应智能门禁",
        logo: "../../images/logo/LB1001.png",
        id: "/pages/LB1001Connect/LB1001Connect",
      },
    ],
  },
  router(e: any) {
    wx.showLoading({ title: "加载中.." });
    const index = e.currentTarget.dataset.index;
    const id: string = this.data.list[index].id;
    if (index == 0 || index == 3) {
      wx.navigateTo({
        url: id,
      });
      wx.hideLoading();
      return;
    }
    wx.navigateToMiniProgram({
      appId: this.data.list[index].id,
      envVersion: "release",
      success: (e) => {
        console.log(e);
        wx.hideLoading();
      },
      fail: (e) => {
        console.log(e);
        wx.showToast({ title: "已取消", icon: "none" });
      },
    });
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {},

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // const isFirst = wx.getStorageSync('isFirst');
    // if (isFirst == '') { //当这个值为空的时候 wx.getStorageSync('isFirsts') == false //true
    //   wx.setStorageSync('isFirst',true)
    // }

    // this.getOpenid();
  },

  getOpenid() {
    wx.login({
      success: (res) => {
        console.log("code 信息 -->", res);
        getOpenidAPI({ code: res.code })
          .then(async (res) => {
            console.log("openid -->", res);
            
          })
          .catch((err) => {
            console.log("获取openid失败-->", err);
          });
      },
      fail: (err) => {
        console.log("调用失败 -->", err);
      },
    });
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
});
