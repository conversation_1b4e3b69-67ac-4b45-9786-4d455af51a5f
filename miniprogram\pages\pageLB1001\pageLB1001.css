/* pages/pageBL1001/pageBL1001.wxss */
page {
  background: linear-gradient(186deg, #49b0fae1, 11%, #f7f7f7 40%);
  height: auto !important;
}
scroll-view ::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
  display: none;
}
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
  display: none;
}
.head {
  background-color: white;
  padding-top: 100rpx;
  padding-bottom: 20rpx;
  height: fit-content;
  border-bottom: 2px solid #cccbcb;
}
.temp {
  text-align: center;
}
.title {
  color: #3c3c3c;
}
.header {
  padding-top: 3vw;
  width: 100%;
  height: 20vh;
  position: relative;
}
.doorContainer {
  position: relative;
  margin-left: 5%;
  width: 90%;
  height: 100%;
  overflow: hidden;
}
.doorContainer > image {
  object-fit: contain !important;
}
.door {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: contain;
  z-index: 10;
}
.door_left {
  position: absolute;
  left: -25%;
  z-index: 99;
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: all 1s;
  z-index: 1;
}
.door_right {
  position: absolute;
  z-index: 99;
  left: 25%;
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: all 1s;
  z-index: 1;
}
.statusText {
  position: absolute;
  bottom: 2%;
  text-align: center;
  color: #66b0fa;
  font-weight: bold;
  font-size: 38rpx;
  width: 100%;
  padding: 20rpx 0rpx;
  z-index: 999;
}
.item {
  margin-top: 30rpx;
  width: 90%;
  margin-left: 5%;
  padding: 30rpx 0rpx;
  background-color: white;
  box-shadow: 2px 0 4px #ccc;
  border-radius: 8px;
  display: flex;
}
.open_and_close_door {
  width: 90%;
  margin-left: 5%;
  display: flex;
  place-items: center;
}
.update-pwd {
  width: 90%;
  margin: 0 auto;
  margin-top: 30rpx;
  padding: 30rpx 0rpx;
  background-color: white;
  box-shadow: 2px 0 4px #ccc;
  border-radius: 8px;
  text-align: center;
  color: #333333;
  font-size: 40rpx;
  margin-bottom: 20px;
}
.open_and_close_door > view {
  width: 43%;
  background-color: white;
  padding: 1.9vh 0rpx;
  border-radius: 8px;
  text-align: center;
  color: #333333;
  font-size: 40rpx;
  box-shadow: 2px 0px 4px #ccc;
  letter-spacing: 10px;
}
.blue {
  background-color: #0B83FF !important;
  color: white !important;
}
.label {
  font-size: 36rpx;
  color: #464646;
  letter-spacing: 1px;
  display: flex;
  place-items: center;
}
.label2 {
  margin-left: auto;
  font-size: 28rpx;
  color: #7A7A7A;
  letter-spacing: 1px;
  display: flex;
}
.btn {
  width: 420rpx !important;
  padding: 20rpx 0px;
  border-radius: 30px;
  background-color: #42a0fd;
  color: white;
  box-shadow: 0px 0 6px #ccc;
}
wx-slider .wx-slider-handle-wrapper {
  height: 16rpx;
  background-color: #E8E8E8 !important;
}
/* swtich整体大小 */
.wx-switch-input {
  width: 100rpx !important;
  height: 52rpx !important;
}
/* false的样式 */
.wx-switch-input::before {
  width: 100rpx !important;
  height: 41rpx !important;
}
/* 圆球的样式 */
.wx-switch-input::after {
  margin-top: 2rpx;
  margin-left: 2rpx;
  width: 56rpx !important;
  height: 41rpx !important;
}
.toast {
  position: fixed;
  top: 0%;
  left: 0%;
  width: 100%;
  height: 100%;
  z-index: 999;
}
.mask {
  position: fixed;
  top: 0%;
  left: 0%;
  width: 100%;
  height: 100%;
  background-color: #3038495e;
  z-index: 999;
}
.toastBox {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 55%;
  height: 23%;
  background-color: white;
  border-radius: 30rpx;
  box-shadow: 0 0 2px gray;
  z-index: 1000;
}
.toast_close {
  position: absolute;
  top: -90rpx;
  right: -30rpx;
  width: 80rpx;
  height: 80rpx;
  line-height: 65rpx;
  color: rgba(0, 0, 0, 0.5);
  background-color: #b4b4b4;
  box-shadow: 0 0 2px #ccc;
  font-size: 70rpx;
  border-radius: 50%;
  text-align: center;
  z-index: 1001;
}
.toast_icon {
  width: 100%;
  height: 80%;
  display: flex;
  justify-content: center;
  place-items: center;
}
.toast_icon > image {
  width: 60%;
  height: 80%;
  object-fit: contain;
}
.toast_text {
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 40rpx;
  color: #0B83FF;
  line-height: 56rpx;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
.blue {
  color: #0B83FF;
}
.red {
  color: crimson;
}
.toastBox-a {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  height: 22%;
  background-color: white;
  border-radius: 30rpx;
  box-shadow: 0 0 2px gray;
  z-index: 1000;
}
.toast_head {
  text-align: center;
  margin-top: 40rpx;
  font-weight: bolder;
}
.toast_input {
  text-align: center;
  font-size: 40rpx;
  color: #0B83FF;
  margin: 45rpx 45rpx;
  border: 1px solid #d4d4d4;
  padding: 15rpx 15rpx;
  width: 80%;
}
.toast_but {
  display: flex;
  width: 90%;
  justify-content: center;
}
.toast_item-a {
  color: #3b3b3b;
  background-color: #a8a8a8;
}
.toast_item-b {
  color: #fff;
  background-color: #0B83FF;
}
.toast_text-a {
  color: #555454;
  margin: 50rpx 50rpx;
}
.pageBox {
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
}
.other-btn {
  display: flex;
  flex-direction: row-reverse;
  margin-right: 20rpx;
  margin-top: 20rpx;
  font-size: 34rpx;
}
.item-tip {
  margin-bottom: 15rpx;
  text-align: center;
  color: #a8a8a8;
}
.pb {
  padding: 30rpx 0 70rpx;
}
.tip {
  width: 80%;
  margin-left: 56rpx;
  margin-top: -60rpx;
  padding: 10rpx 20rpx;
  color: #a8a8a8;
  font-size: 28rpx;
  text-align: left;
  display: block;
}
.wenhao {
  margin-left: 10rpx;
  background-color: gray;
  border-radius: 50%;
  width: 46rpx;
  height: 46rpx;
  color: white;
  position: relative;
  text-align: center;
  display: flex;
  justify-content: center;
  place-items: center;
}
.bubbleToast {
  position: absolute;
  top: -220rpx;
  left: 61.8%;
  width: 52vw;
  padding: 20rpx;
  border-radius: 8px;
  background-color: white;
  color: #3b3b3b;
  z-index: 99999;
  border: 2px solid #3d7cb1;
  box-shadow: 2px 2px 4px skyblue;
  font-size: 28rpx;
  text-align: left;
  text-indent: 0rpx;
}
.bubbleToast::before {
  content: '';
  width: 0;
  height: 0;
  border: 20px solid;
  position: absolute;
  bottom: -40px;
  left: 20%;
  border-color: #3d7cb1 transparent transparent;
}
.bubbleToast::after {
  content: '';
  width: 0;
  height: 0;
  border: 20px solid;
  position: absolute;
  bottom: -36px;
  left: 20%;
  border-color: #fff transparent transparent;
}
.mask_bubble {
  position: fixed;
  top: 0%;
  left: 0%;
  width: 100%;
  height: 100%;
  z-index: 999;
}
.cm {
  position: absolute;
  width: 30px;
  height: 30px;
  z-index: 1000;
  bottom: 0px;
  left: calc(50% - 4px);
  color: #3d3d3d;
}
.cm::before {
  content: " ";
  position: absolute;
  width: 10px;
  height: 10px;
  z-index: 1000;
  bottom: 32px;
  background-color: red;
  border-radius: 50%;
  box-shadow: 0 0 2px 2px #d33333;
}
.radar {
  position: relative;
  top: 0;
  width: 200px;
  height: 200px;
  margin: -190rpx auto 40rpx;
  background-color: #f0f0f0;
  border-radius: 50%;
  border: 2px solid #ccc;
  overflow: hidden;
  background-image: radial-gradient(circle at center, transparent 20%, #ccc 20%, #ccc 21%, transparent 21%), radial-gradient(circle at center, transparent 40%, #ccc 40%, #ccc 41%, transparent 41%), radial-gradient(circle at center, transparent 60%, #ccc 60%, #ccc 61%, transparent 61%), radial-gradient(circle at center, transparent 80%, #ccc 80%, #ccc 81%, transparent 81%), linear-gradient(to right, transparent 49.5%, #ccc 49.5%, #ccc 50.5%, transparent 50.5%), linear-gradient(to bottom, transparent 49.5%, #ccc 49.5%, #ccc 50.5%, transparent 50.5%), linear-gradient(45deg, transparent 49%, #ccc 49%, #ccc 51%, transparent 51%), linear-gradient(-45deg, transparent 49%, #ccc 49%, #ccc 51%, transparent 51%);
  /* 裁剪出下半圆：使用多边形裁剪，创建平顶效果 */
  clip-path: polygon(0% 50%, 100% 50%, 100% 100%, 0% 100%);
}
.radar-scan {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: conic-gradient(from 0deg, transparent 0%, rgba(73, 176, 250, 0.5) 90%, transparent 100%);
  animation: radar-sweep 4s linear infinite;
  transform-origin: center;
}
.radar::after {
  position: absolute;
  content: "大门";
  top: 56%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-weight: bold;
}
.forgetPwd {
  margin: 20rpx 56rpx 0rpx 0;
  text-align: right;
  color: #42a0fd;
}
.forgetPwd2 {
  margin-right: 56rpx;
  text-align: right;
  color: #42a0fd;
  margin-bottom: -20rpx;
}
@keyframes radar-sweep {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
